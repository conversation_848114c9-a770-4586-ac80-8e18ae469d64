import { NestFactory } from '@nestjs/core';
import { Transport, MicroserviceOptions } from '@nestjs/microservices';
import { AppModule } from './app.module';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // Temporarily disable <PERSON><PERSON><PERSON> for testing
  // app.connectMicroservice<MicroserviceOptions>({
  //   transport: Transport.KAFKA,
  //   options: {
  //     client: {
  //       brokers: ['localhost:9092'],
  //     },
  //     consumer: {
  //       groupId: 'campaign-consumer',
  //     },
  //   },
  // });

  // await app.startAllMicroservices();
  await app.listen(3001);
}
bootstrap();
