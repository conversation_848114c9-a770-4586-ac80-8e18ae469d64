import { Injectable } from '@nestjs/common';
import { v4 as uuidv4 } from 'uuid';
import { Campaign } from './entities/campaign.entity';
import { UserPoints } from './entities/user-points.entity';
import { CampaignRepository, UserPointsRepository, ProcessedOrderRepository } from './repositories';

@Injectable()
export class AppService {
  constructor(
    private readonly campaignRepository: CampaignRepository,
    private readonly userPointsRepository: UserPointsRepository,
    private readonly processedOrderRepository: ProcessedOrderRepository,
  ) {}

  createCampaign(name: string, totalPoints: number, startDate: Date, endDate: Date): Campaign {
    const campaign: Campaign = {
      id: uuidv4(),
      name,
      totalPoints,
      remainingPoints: totalPoints,
      startDate,
      endDate,
      status: 'active',
    };

    this.campaigns.push(campaign);
    return campaign;
  }

  async handleOrderCreated(orderData: { orderId: string; userId: string; timestamp: Date }) {
    // Ensure order hasn't been processed before (idempotency)
    if (this.processedOrders.has(orderData.orderId)) {
      return;
    }

    const activeCampaign = this.campaigns.find(
      (campaign) =>
        campaign.status === 'active' &&
        campaign.remainingPoints > 0 &&
        campaign.startDate <= orderData.timestamp &&
        campaign.endDate >= orderData.timestamp,
    );

    if (!activeCampaign) {
      return;
    }

    // Default points per order
    const pointsToAward = 10;

    if (activeCampaign.remainingPoints >= pointsToAward) {
      const userPoints: UserPoints = {
        userId: orderData.userId,
        campaignId: activeCampaign.id,
        points: pointsToAward,
        orderId: orderData.orderId,
        timestamp: orderData.timestamp,
      };

      this.userPoints.push(userPoints);
      activeCampaign.remainingPoints -= pointsToAward;
      this.processedOrders.add(orderData.orderId);

      if (activeCampaign.remainingPoints === 0) {
        activeCampaign.status = 'completed';
      }

      return userPoints;
    }
  }

  getCampaigns(): Campaign[] {
    return this.campaigns;
  }

  getUserPoints(userId: string): UserPoints[] {
    return this.userPoints.filter((up) => up.userId === userId);
  }
}
