#!/bin/bash

echo "🐳 Starting Kafka Cluster and Microservices..."

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
    else
        echo -e "${RED}❌ $2${NC}"
    fi
}

echo -e "${YELLOW}Building and starting services...${NC}"

# Build and start all services
docker-compose up --build -d

if [ $? -eq 0 ]; then
    print_status 0 "All services started successfully"
else
    print_status 1 "Failed to start services"
    exit 1
fi

echo ""
echo -e "${BLUE}📊 Service Status:${NC}"
docker-compose ps

echo ""
echo -e "${BLUE}🔗 Available Services:${NC}"
echo "  📊 Kafka UI:           http://localhost:8080"
echo "  🛒 Order Service:      http://localhost:3000"
echo "  🎯 Campaign Service:   http://localhost:3001"
echo "  🌐 API Gateway:        http://localhost:3002"
echo ""
echo -e "${BLUE}📡 Kafka Brokers:${NC}"
echo "  🔸 Kafka1:             localhost:9092"
echo "  🔸 Kafka2:             localhost:9093"
echo "  🔸 Kafka3:             localhost:9094"
echo ""
echo -e "${BLUE}🗄️ Database Volumes:${NC}"
echo "  📁 Order DB:           order-db volume"
echo "  📁 Campaign DB:        campaign-db volume"
echo ""
echo -e "${YELLOW}Waiting for services to be ready...${NC}"
sleep 10

echo ""
echo -e "${BLUE}🧪 Testing Services:${NC}"

# Test Order Service
echo -n "Testing Order Service... "
RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000/courses)
if [ "$RESPONSE" = "200" ]; then
    print_status 0 "Order Service is ready"
else
    print_status 1 "Order Service not ready (HTTP $RESPONSE)"
fi

# Test Campaign Service
echo -n "Testing Campaign Service... "
RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3001/campaigns)
if [ "$RESPONSE" = "200" ]; then
    print_status 0 "Campaign Service is ready"
else
    print_status 1 "Campaign Service not ready (HTTP $RESPONSE)"
fi

echo ""
echo -e "${GREEN}🎉 Setup completed!${NC}"
echo ""
echo -e "${YELLOW}📝 Next steps:${NC}"
echo "  1. Visit Kafka UI: http://localhost:8080"
echo "  2. Test APIs with the provided endpoints"
echo "  3. Check logs: docker-compose logs -f [service-name]"
echo "  4. Stop services: ./docker-stop.sh"
echo ""
echo -e "${BLUE}💡 Useful commands:${NC}"
echo "  docker-compose logs -f order-service"
echo "  docker-compose logs -f campaign-service"
echo "  docker-compose logs -f kafka1"
