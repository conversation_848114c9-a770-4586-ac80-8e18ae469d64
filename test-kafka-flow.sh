#!/bin/bash

echo "🧪 Testing Complete Kafka Flow..."

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
    else
        echo -e "${RED}❌ $2${NC}"
    fi
}

echo -e "${BLUE}🎯 Step 1: Create a Campaign${NC}"
CAMPAIGN_RESPONSE=$(curl -s -X POST http://localhost:3001/campaigns \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Docker Test Campaign",
    "totalPoints": 1000,
    "startDate": "2025-01-01T00:00:00.000Z",
    "endDate": "2025-12-31T23:59:59.000Z"
  }')

if [ $? -eq 0 ]; then
    print_status 0 "Campaign created successfully"
    echo "Response: $CAMPAIGN_RESPONSE"
else
    print_status 1 "Failed to create campaign"
    exit 1
fi

echo ""
echo -e "${BLUE}🛒 Step 2: Create an Order (triggers Kafka message)${NC}"
ORDER_RESPONSE=$(curl -s -X POST http://localhost:3000/orders \
  -H "Content-Type: application/json" \
  -d '{"userId": "docker-user-123", "courseId": "1"}')

if [ $? -eq 0 ]; then
    print_status 0 "Order created successfully"
    echo "Response: $ORDER_RESPONSE"
else
    print_status 1 "Failed to create order"
    exit 1
fi

echo ""
echo -e "${YELLOW}⏳ Waiting for Kafka message processing...${NC}"
sleep 5

echo ""
echo -e "${BLUE}🏆 Step 3: Check User Points (should be awarded via Kafka)${NC}"
POINTS_RESPONSE=$(curl -s http://localhost:3001/points/docker-user-123)

if [ $? -eq 0 ]; then
    print_status 0 "Points retrieved successfully"
    echo "Response: $POINTS_RESPONSE"
    
    # Check if points were awarded
    if [[ $POINTS_RESPONSE == *"points"* ]]; then
        print_status 0 "Kafka flow working - points were awarded!"
    else
        print_status 1 "Kafka flow issue - no points awarded"
    fi
else
    print_status 1 "Failed to retrieve points"
fi

echo ""
echo -e "${BLUE}📊 Step 4: Check All Data${NC}"

echo -e "${YELLOW}All Campaigns:${NC}"
curl -s http://localhost:3001/campaigns | jq '.' 2>/dev/null || curl -s http://localhost:3001/campaigns

echo ""
echo -e "${YELLOW}All Orders:${NC}"
curl -s http://localhost:3000/orders | jq '.' 2>/dev/null || curl -s http://localhost:3000/orders

echo ""
echo -e "${YELLOW}All Courses:${NC}"
curl -s http://localhost:3000/courses | jq '.' 2>/dev/null || curl -s http://localhost:3000/courses

echo ""
echo -e "${GREEN}🎉 Kafka flow test completed!${NC}"
echo ""
echo -e "${BLUE}💡 Check Kafka UI for message details:${NC}"
echo "  http://localhost:8080"
