import { FactoryProvider, ModuleMetadata, Provider, Type } from '@nestjs/common';
import { AxiosRequestConfig } from 'axios';
export type HttpModuleOptions = AxiosRequestConfig & {
    global?: boolean;
};
export interface HttpModuleOptionsFactory {
    createHttpOptions(): Promise<HttpModuleOptions> | HttpModuleOptions;
}
export interface HttpModuleAsyncOptions extends Pick<ModuleMetadata, 'imports'> {
    useExisting?: Type<HttpModuleOptionsFactory>;
    useClass?: Type<HttpModuleOptionsFactory>;
    useFactory?: (...args: any[]) => Promise<HttpModuleOptions> | HttpModuleOptions;
    inject?: FactoryProvider['inject'];
    extraProviders?: Provider[];
    global?: boolean;
}
